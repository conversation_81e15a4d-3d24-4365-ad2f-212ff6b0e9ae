# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: 视频.py
# Bytecode version: 3.12.0rc2 (3531)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import subprocess
import json
import os
import sys
import tempfile
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QTextEdit, QFileDialog, QProgressBar, QMessageBox, QFrame, QSizePolicy, QComboBox, QCheckBox
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont, QPainter, QLinearGradient, QColor, QPen, QIcon, QPixmap
import base64
try:
    from logo import imgBase64
except ImportError:
    pass
else:
    def set_local_app_icon(app):
        """设置本地文件图标的备用函数"""
        try:
            icon_paths = ['luffy.ico', 'luffy.png', 'icon.ico', 'icon.png']
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    icon = QIcon(icon_path)
                    if not icon.isNull():
                        app.setWindowIcon(icon)
                        print(f'✅ 已设置应用图标: {icon_path}')
                        return True
            print('⚠️ 未找到本地图标文件')
            return False
        except Exception as e:
            print(f'设置本地图标失败: {str(e)}')
            return False

    def set_luffy_icon(window):
        """为窗口设置路飞图标"""
        try:
            if imgBase64:
                image_data = base64.b64decode(imgBase64)
                pixmap = QPixmap()
                pixmap.loadFromData(image_data)
                if not pixmap.isNull():
                    icon = QIcon(pixmap)
                    window.setWindowIcon(icon)
                    return True
            icon_paths = ['luffy.ico', 'luffy.png', 'icon.ico', 'icon.png']
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    icon = QIcon(icon_path)
                    if not icon.isNull():
                        window.setWindowIcon(icon)
                        return True
            return False
        except Exception as e:
            print(f'设置图标失败: {str(e)}')
            return False

    class GradientLabel(QLabel):
        """自定义渐变字体标签"""
        def __init__(self, text='', parent=None):
            super().__init__(text, parent)
            self.gradient_colors = [QColor(131, 58, 180), QColor(253, 29, 29), QColor(252, 176, 69)]

        def paintEvent(self, event):
            painter = QPainter(self)
            painter.setRenderHint(QPainter.Antialiasing)
            gradient = QLinearGradient(0, 0, self.width(), 0)
            gradient.setColorAt(0.0, self.gradient_colors[0])
            gradient.setColorAt(0.5, self.gradient_colors[1])
            gradient.setColorAt(1.0, self.gradient_colors[2])
            pen = QPen()
            pen.setBrush(gradient)
            pen.setWidth(2)
            painter.setPen(pen)
            font = self.font()
            painter.setFont(font)
            painter.drawText(self.rect(), self.alignment(), self.text())

class VideoProcessor(QThread):
    progress_signal = pyqtSignal(int, str)
    finished_signal = pyqtSignal(bool, str)
    task_finished_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.use_gpu = False
        self.gpu_available = False
        self.processing_mode = 'standard'
        self.current_process = None
        self.total_tasks = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.temp_dir = None

    def set_processing_mode(self, mode):
        self.processing_mode = mode

    def set_gpu_acceleration(self, use_gpu):
        self.use_gpu = use_gpu

    def check_gpu_support(self):
        # 检查GPU支持
        try:
            result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            if result.returncode == 0:
                self.gpu_available = True
            else:
                self.gpu_available = False
        except Exception:
            self.gpu_available = False

    def _test_nvenc_encoding(self):
        # 测试NVENC编码
        try:
            result = subprocess.run(['ffmpeg', '-encoders'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return b'nvenc' in result.stdout
        except Exception:
            return False

    def _test_amf_encoding(self):
        # 测试AMF编码
        try:
            result = subprocess.run(['ffmpeg', '-encoders'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return b'amf' in result.stdout
        except Exception:
            return False

    def get_detailed_gpu_info(self):
        # 获取详细GPU信息
        try:
            result = subprocess.run(['nvidia-smi', '-q'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            return result.stdout.decode('utf-8')
        except Exception:
            return ''

    def get_video_encoder(self):
        # 获取视频编码器
        if self.use_gpu and self.gpu_available:
            return 'h264_nvenc'
        return 'libx264'

    def get_encoder_preset(self):
        # 获取编码器预设
        if self.use_gpu and self.gpu_available:
            return 'p7'
        return 'medium'

    def get_quality_params(self):
        # 获取质量参数
        if self.use_gpu and self.gpu_available:
            return ['-cq', '19']
        return ['-crf', '23']

    def get_decode_params(self):
        # 获取解码参数
        if self.use_gpu and self.gpu_available:
            return ['-hwaccel', 'auto']
        return []

    def get_ffmpeg_path(self):
        # 获取FFmpeg路径
        return 'ffmpeg'

    def get_video_metadata(self, input_file):
        # 获取视频元数据
        try:
            result = subprocess.run([
                self.get_ffmpeg_path(),
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_streams',
                '-show_format',
                '-i', input_file
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=10)
            stdout = result.stdout.decode('utf-8')
        except UnicodeDecodeError:
            return {'width': 1080, 'height': 1920, 'duration': 2.0}
        except Exception:
            return {'width': 1080, 'height': 1920, 'duration': 2.0}
        try:
            metadata = json.loads(stdout)
            stream = metadata['streams'][0]
            width = int(stream.get('width', 1080))
            height = int(stream.get('height', 1920))
            duration = float(stream.get('duration', 2.0))
            return {'width': width, 'height': height, 'duration': duration}
        except (json.JSONDecodeError, IndexError, KeyError, ValueError):
            return {'width': 1080, 'height': 1920, 'duration': 2.0}

    def run_ffmpeg(self, cmd, progress_start, progress_end, total_frames=None):
        # 运行FFmpeg命令
        try:
            self.current_process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            self.progress_signal.emit(progress_start, '开始处理...')
            self.current_process.wait()
            self.progress_signal.emit(progress_end, '处理完成')
        except Exception as e:
            self.progress_signal.emit(progress_start, f'处理出错: {str(e)}')
        finally:
            self.current_process = None

    def run(self):
        self.running = True
        self.completed_tasks = 0
        self.failed_tasks = 0
        if self.use_gpu:
            self.check_gpu_support()
            if not self.gpu_available:
                self.progress_signal.emit(0, '切换到标准处理模式')
                self.use_gpu = False
        self._prepare_tasks()
        processing_info = '高速处理模式' if self.use_gpu and self.gpu_available else '标准处理模式'
        self.progress_signal.emit(10, f'准备处理 {self.total_tasks} 个任务，使用 {processing_info}')
        for video_path, image_path in self._match_files():
            if not self.running:
                break
            video_name = os.path.basename(video_path)
            self.progress_signal.emit(20, f'正在处理: {video_name}')
            metadata = self.get_video_metadata(video_path)
            try:
                self._process_task_high_quality(video_path, image_path, metadata)
                self.completed_tasks += 1
                self.task_finished_signal.emit(video_name)
            except Exception as e:
                self.failed_tasks += 1
                self.progress_signal.emit(0, f'任务失败: {video_name}, 错误: {str(e)}')
        self.finished_signal.emit(self.failed_tasks == 0, f'全部完成，成功: {self.completed_tasks}，失败: {self.failed_tasks}')

    def stop(self):
        """停止处理"""
        self.running = False
        if self.current_process and self.current_process.poll() is None:
            try:
                self.progress_signal.emit(0, '正在强制停止FFmpeg进程...')
                self.current_process.terminate()
                self.current_process.wait(timeout=3)
            except subprocess.TimeoutExpired:
                pass
            else:
                self.current_process = None
            try:
                self.current_process.kill()
                self.current_process.wait(timeout=2)
                self.progress_signal.emit(0, 'FFmpeg进程已强制终止')
            except:
                self.progress_signal.emit(0, '无法终止FFmpeg进程，请手动结束')
        except Exception as e:
            self.progress_signal.emit(0, f'停止进程时出错: {str(e)}')

    def _prepare_tasks(self):
        # 任务准备逻辑
        pass

    def _get_timed_files(self, folder, is_video=False):
        files = []
        for filename in os.listdir(folder):
            filepath = os.path.join(folder, filename)
            if os.path.isfile(filepath):
                try:
                    mtime = os.path.getmtime(filepath)
                    files.append({'path': filepath, 'mtime': mtime, 'name': filename})
                except Exception as e:
                    pass
        return files

    def _ensure_unicode_path(self, path):
        if isinstance(path, str):
            return path
        try:
            return path.decode('utf-8')
        except Exception:
            return path

    def _format_time(self, timestamp):
        import datetime
        return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

    def _match_files(self):
        # 匹配文件逻辑
        return []

    def _process_task_high_quality(self, video_path, image_path, metadata):
        # 高质量处理任务逻辑
        pass

    def _cleanup_temp_dir(self, temp_dir):
        try:
            if os.path.exists(temp_dir):
                for filename in os.listdir(temp_dir):
                    file_path = os.path.join(temp_dir, filename)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                os.rmdir(temp_dir)
        except Exception as e:
            print(f'清理临时目录失败: {str(e)}') 